// Test script untuk memverifikasi API downloader
const { Downloader } = require('abot-scraper');

const downloader = new Downloader();

// Test URLs untuk berbagai platform
const testUrls = {
    tiktok: [
        'https://www.tiktok.com/@username/video/1234567890123456789',
        'https://vm.tiktok.com/ZMexample/'
    ],
    youtube: [
        'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        'https://youtu.be/dQw4w9WgXcQ'
    ],
    instagram: [
        'https://www.instagram.com/p/ABC123/',
        'https://www.instagram.com/reel/DEF456/'
    ],
    facebook: [
        'https://www.facebook.com/watch/?v=123456789'
    ]
};

// Fungsi untuk test TikTok downloader
async function testTikTokDownloader(url) {
    console.log(`🧪 Testing TikTok downloader with: ${url}`);
    
    try {
        const result = await downloader.tiktokDownloader(url);
        console.log('📊 TikTok Result:', JSON.stringify(result, null, 2));
        
        if (result.status === 200 && result.result && result.result.video) {
            console.log('✅ TikTok downloader working correctly');
            console.log(`🎥 Video URL: ${result.result.video}`);
            console.log(`📝 Title: ${result.result.title || 'No title'}`);
            console.log(`👤 Author: ${result.result.author || 'No author'}`);
            return { success: true, videoUrl: result.result.video };
        } else {
            console.log('❌ TikTok downloader failed:', result.msg || 'Unknown error');
            return { success: false, error: result.msg || 'Unknown error' };
        }
    } catch (error) {
        console.log('❌ TikTok downloader error:', error.message);
        return { success: false, error: error.message };
    }
}

// Fungsi untuk test YouTube downloader
async function testYouTubeDownloader(url) {
    console.log(`🧪 Testing YouTube downloader with: ${url}`);
    
    try {
        const result = await downloader.youtubeDownloader(url);
        console.log('📊 YouTube Result:', JSON.stringify(result, null, 2));
        
        if (result.status === 200 && result.result && result.result.video) {
            console.log('✅ YouTube downloader working correctly');
            console.log(`🎥 Video URL: ${result.result.video}`);
            console.log(`📝 Title: ${result.result.title || 'No title'}`);
            return { success: true, videoUrl: result.result.video };
        } else {
            console.log('❌ YouTube downloader failed:', result.msg || 'Unknown error');
            return { success: false, error: result.msg || 'Unknown error' };
        }
    } catch (error) {
        console.log('❌ YouTube downloader error:', error.message);
        return { success: false, error: error.message };
    }
}

// Fungsi untuk test Instagram downloader
async function testInstagramDownloader(url) {
    console.log(`🧪 Testing Instagram downloader with: ${url}`);
    
    try {
        const result = await downloader.instagramDownloader(url);
        console.log('📊 Instagram Result:', JSON.stringify(result, null, 2));
        
        if (result.status === 200 && result.result && result.result.video) {
            console.log('✅ Instagram downloader working correctly');
            console.log(`🎥 Video URL: ${result.result.video}`);
            return { success: true, videoUrl: result.result.video };
        } else {
            console.log('❌ Instagram downloader failed:', result.msg || 'Unknown error');
            return { success: false, error: result.msg || 'Unknown error' };
        }
    } catch (error) {
        console.log('❌ Instagram downloader error:', error.message);
        return { success: false, error: error.message };
    }
}

// Fungsi untuk test Facebook downloader
async function testFacebookDownloader(url) {
    console.log(`🧪 Testing Facebook downloader with: ${url}`);
    
    try {
        const result = await downloader.facebookDownloader(url);
        console.log('📊 Facebook Result:', JSON.stringify(result, null, 2));
        
        if (result.status === 200 && result.result && result.result.video) {
            console.log('✅ Facebook downloader working correctly');
            console.log(`🎥 Video URL: ${result.result.video}`);
            return { success: true, videoUrl: result.result.video };
        } else {
            console.log('❌ Facebook downloader failed:', result.msg || 'Unknown error');
            return { success: false, error: result.msg || 'Unknown error' };
        }
    } catch (error) {
        console.log('❌ Facebook downloader error:', error.message);
        return { success: false, error: error.message };
    }
}

// Fungsi untuk test YouTube MP3 downloader
async function testYouTubeMP3Downloader(url) {
    console.log(`🧪 Testing YouTube MP3 downloader with: ${url}`);
    
    try {
        const result = await downloader.ytMp3Downloader(url);
        console.log('📊 YouTube MP3 Result:', JSON.stringify(result, null, 2));
        
        if (result.status === 200 && result.result && result.result.audio) {
            console.log('✅ YouTube MP3 downloader working correctly');
            console.log(`🎵 Audio URL: ${result.result.audio}`);
            console.log(`📝 Title: ${result.result.title || 'No title'}`);
            return { success: true, audioUrl: result.result.audio };
        } else {
            console.log('❌ YouTube MP3 downloader failed:', result.msg || 'Unknown error');
            return { success: false, error: result.msg || 'Unknown error' };
        }
    } catch (error) {
        console.log('❌ YouTube MP3 downloader error:', error.message);
        return { success: false, error: error.message };
    }
}

// Main test function
async function runAllTests() {
    console.log('🚀 Starting API Downloader Tests...\n');
    
    const results = {
        tiktok: [],
        youtube: [],
        instagram: [],
        facebook: [],
        youtubeMP3: []
    };
    
    // Test TikTok
    console.log('📱 Testing TikTok Downloader...');
    for (const url of testUrls.tiktok) {
        const result = await testTikTokDownloader(url);
        results.tiktok.push(result);
        console.log('─'.repeat(50));
    }
    
    // Test YouTube
    console.log('🎥 Testing YouTube Downloader...');
    for (const url of testUrls.youtube) {
        const result = await testYouTubeDownloader(url);
        results.youtube.push(result);
        console.log('─'.repeat(50));
    }
    
    // Test YouTube MP3
    console.log('🎵 Testing YouTube MP3 Downloader...');
    for (const url of testUrls.youtube) {
        const result = await testYouTubeMP3Downloader(url);
        results.youtubeMP3.push(result);
        console.log('─'.repeat(50));
    }
    
    // Test Instagram
    console.log('📷 Testing Instagram Downloader...');
    for (const url of testUrls.instagram) {
        const result = await testInstagramDownloader(url);
        results.instagram.push(result);
        console.log('─'.repeat(50));
    }
    
    // Test Facebook
    console.log('📘 Testing Facebook Downloader...');
    for (const url of testUrls.facebook) {
        const result = await testFacebookDownloader(url);
        results.facebook.push(result);
        console.log('─'.repeat(50));
    }
    
    // Summary
    console.log('📊 TEST SUMMARY:');
    console.log('═'.repeat(50));
    
    Object.keys(results).forEach(platform => {
        const platformResults = results[platform];
        const successCount = platformResults.filter(r => r.success).length;
        const totalCount = platformResults.length;
        console.log(`${platform.toUpperCase()}: ${successCount}/${totalCount} tests passed`);
    });
    
    console.log('✅ All tests completed!');
    return results;
}

// Jalankan test jika file ini dieksekusi langsung
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testTikTokDownloader,
    testYouTubeDownloader,
    testInstagramDownloader,
    testFacebookDownloader,
    testYouTubeMP3Downloader,
    runAllTests
};
