// JVG DOWNLOAD BOT - Bot Telegram dengan fitur lengkap
// Install: npm install telegraf nik-parser abot-scraper axios wa-sticker-formatter

const { Telegraf } = require('telegraf');
const { nikParser } = require('nik-parser');
const { Downloader, Search, Tools } = require('abot-scraper');
const { Sticker, StickerTypes } = require('wa-sticker-formatter');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Load environment variables
function loadEnv() {
  try {
    const envPath = path.join(__dirname, '.env');
    if (fs.existsSync(envPath)) {
      const envFile = fs.readFileSync(envPath, 'utf8');
      const lines = envFile.split('\n');
      lines.forEach(line => {
        const [key, value] = line.split('=');
        if (key && value) {
          process.env[key.trim()] = value.trim();
        }
      });
    }
  } catch (error) {
    console.warn('Warning: Could not load .env file');
  }
}

loadEnv();

const BOT_TOKEN = process.env.BOT_TOKEN || '7839973328:AAGk2MtpS3jj0lLJgxZWxG2lQxEqSgDn7mw';
const bot = new Telegraf(BOT_TOKEN);
const downloader = new Downloader();
const search = new Search();
const tools = new Tools();

// Rate limiting
const userLastRequest = new Map();

// Check rate limiting
function checkRateLimit(userId) {
  const now = Date.now();
  const lastRequest = userLastRequest.get(userId);
  
  if (lastRequest && (now - lastRequest) < 3000) {
    return false;
  }
  
  userLastRequest.set(userId, now);
  return true;
}

// Download and send video function
async function downloadAndSendVideo(ctx, videoUrl, platform) {
  let filePath = null;
  
  try {
    const urlPattern = /^https?:\/\/.+/;
    if (!urlPattern.test(videoUrl)) {
      throw new Error('URL tidak valid');
    }
    
    const tempDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    const fileName = `video_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.mp4`;
    filePath = path.join(tempDir, fileName);
    
    // Download with streaming
    const response = await axios({
      method: 'GET',
      url: videoUrl,
      responseType: 'stream',
      timeout: 60000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    const writer = fs.createWriteStream(filePath);
    response.data.pipe(writer);
    
    await new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
      response.data.on('error', reject);
    });
    
    await ctx.replyWithVideo({
      source: filePath,
      caption: `📹 Video ${platform} berhasil didownload`
    });
    
  } catch (error) {
    console.error(`${platform} download error:`, error);
    await ctx.reply(`❌ Gagal mengirim video ${platform}: ${error.message}`);
  } finally {
    if (filePath && fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
        console.log(`Temporary file deleted: ${filePath}`);
      } catch (cleanupError) {
        console.error('Error deleting temporary file:', cleanupError);
      }
    }
  }
}

// Download MP3 from YouTube
async function downloadAndSendMP3(ctx, videoUrl) {
  let filePath = null;
  
  try {
    const youtubePattern = /^https?:\/\/(www\.)?(youtube\.com|youtu\.be)\/.+/;
    if (!youtubePattern.test(videoUrl)) {
      throw new Error('URL YouTube tidak valid');
    }
    
    await ctx.reply('🎵 **Memproses audio MP3...**\n⏳ Mengekstrak audio berkualitas tinggi dari YouTube...');
    
    const result = await downloader.ytMp3Downloader(videoUrl);
    
    if (result.status !== 200 || !result.result || !result.result.audio) {
      throw new Error(result.msg || 'Gagal mendapatkan audio MP3');
    }
    
    const tempDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    const fileName = `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.mp3`;
    filePath = path.join(tempDir, fileName);
    
    const response = await axios({
      method: 'GET',
      url: result.result.audio,
      responseType: 'stream',
      timeout: 60000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    const writer = fs.createWriteStream(filePath);
    response.data.pipe(writer);
    
    await new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
      response.data.on('error', reject);
    });
    
    await ctx.replyWithAudio({
      source: filePath,
      caption: `🎵 ${result.result.title || 'Audio MP3'} berhasil didownload`
    });
    
  } catch (error) {
    console.error('MP3 download error:', error);
    await ctx.reply(`❌ Gagal mengirim audio MP3: ${error.message}`);
  } finally {
    if (filePath && fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
        console.log(`Temporary MP3 file deleted: ${filePath}`);
      } catch (cleanupError) {
        console.error('Error deleting temporary MP3 file:', cleanupError);
      }
    }
  }
}

// Search YouTube
async function searchYouTube(ctx, query) {
  try {
    await ctx.reply('🔍 **Mencari video di YouTube...**\n⏳ Menganalisis database YouTube untuk hasil terbaik...');
    
    const result = await search.ytSearch(query);
    
    if (result.status !== 200 || !result.result || !Array.isArray(result.result)) {
      throw new Error(result.msg || 'Gagal mencari video');
    }
    
    const videos = result.result.slice(0, 5);
    
    if (videos.length === 0) {
      await ctx.reply('❌ Tidak ditemukan video untuk pencarian tersebut');
      return;
    }
    
    let message = `🎥 **Hasil Pencarian YouTube untuk "${query}":**\n\n`;
    
    videos.forEach((video, index) => {
      message += `${index + 1}. **${video.title}**\n`;
      message += `   👤 ${video.channel}\n`;
      message += `   ⏱️ ${video.duration}\n`;
      message += `   🔗 ${video.url}\n\n`;
    });
    
    message += `💡 **Tip:** Kirim link video untuk download atau ketik "mp3 [link]" untuk download audio saja`;
    
    await ctx.reply(message, { parse_mode: 'Markdown' });
    
  } catch (error) {
    console.error('YouTube search error:', error);
    await ctx.reply(`❌ Gagal mencari video: ${error.message}`);
  }
}

// Remove background with AI
async function removeBackground(ctx, photoBuffer) {
  try {
    await ctx.reply('🎨 **AI Background Remover aktif!**\n⏳ Menganalisis objek dan menghapus background secara otomatis...');
    
    const result = await tools.removeBackground(photoBuffer);
    
    if (result.status !== 200 || !result.result || !result.result.image) {
      throw new Error(result.msg || 'Gagal menghapus background');
    }
    
    const response = await axios.get(result.result.image, { responseType: 'arraybuffer' });
    
    await ctx.replyWithPhoto({
      source: Buffer.from(response.data),
      caption: '✨ Background berhasil dihapus dengan AI!'
    });
    
  } catch (error) {
    console.error('Background removal error:', error);
    await ctx.reply(`❌ Gagal menghapus background: ${error.message}`);
  }
}

// Enhance image with AI
async function enhanceImage(ctx, photoBuffer, version = 'v1') {
  try {
    await ctx.reply(`✨ **AI Image Enhancer ${version.toUpperCase()} aktif!**\n⏳ Menganalisis dan meningkatkan kualitas foto dengan teknologi AI...`);
    
    const result = version === 'v2' 
      ? await tools.reminiV2(photoBuffer)
      : await tools.reminiV1(photoBuffer);
    
    if (result.status !== 200 || !result.result || !result.result.image) {
      throw new Error(result.msg || 'Gagal meningkatkan kualitas gambar');
    }
    
    const response = await axios.get(result.result.image, { responseType: 'arraybuffer' });
    
    await ctx.replyWithPhoto({
      source: Buffer.from(response.data),
      caption: `✨ Kualitas gambar berhasil ditingkatkan dengan AI ${version.toUpperCase()}!`
    });
    
  } catch (error) {
    console.error('Image enhancement error:', error);
    await ctx.reply(`❌ Gagal meningkatkan kualitas gambar: ${error.message}`);
  }
}

// Create sticker from image
async function createStickerFromImage(ctx, photoBuffer) {
  let filePath = null;
  
  try {
    await ctx.reply('🎭 **Sticker Maker aktif!**\n⏳ Mengkonversi foto menjadi sticker WebP berkualitas tinggi...');
    
    // Create sticker using wa-sticker-formatter
    const sticker = new Sticker(photoBuffer, {
      pack: 'JVG DOWNLOAD BOT', // Pack name
      author: 'Telegram Bot', // Author name
      type: StickerTypes.FULL, // Full type (not cropped)
      categories: ['😀', '🎉'], // Sticker categories
      quality: 75 // Quality (0-100)
    });
    
    // Convert to buffer
    const stickerBuffer = await sticker.toBuffer();
    
    // Create temp directory
    const tempDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // Save temporary sticker file
    const fileName = `sticker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.webp`;
    filePath = path.join(tempDir, fileName);
    
    fs.writeFileSync(filePath, stickerBuffer);
    
    // Send sticker to Telegram
    await ctx.replyWithSticker({
      source: filePath,
      caption: '🎉 Sticker berhasil dibuat!'
    });
    
  } catch (error) {
    console.error('Sticker creation error:', error);
    await ctx.reply(`❌ Gagal membuat sticker: ${error.message}`);
  } finally {
    // Cleanup temporary file
    if (filePath && fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
        console.log(`Temporary sticker file deleted: ${filePath}`);
      } catch (cleanupError) {
        console.error('Error deleting temporary sticker file:', cleanupError);
      }
    }
  }
}

// Bot start command
bot.start((ctx) => ctx.reply(`🚀 **Selamat Datang di JVG DOWNLOAD BOT!**

✨ **Bot All-in-One dengan 7 Fitur Canggih:**

🆔 **NIK Checker** - Validasi & info lengkap NIK Indonesia
📹 **Video Downloader** - TikTok, YouTube, Instagram, Facebook
🎵 **MP3 Downloader** - Audio berkualitas dari YouTube
🔍 **YouTube Search** - Cari video tanpa ribet copy link
🎨 **AI Background Remover** - Hapus background otomatis
✨ **AI Image Enhancer** - Tingkatkan kualitas foto dengan AI
🎭 **Sticker Maker** - Buat sticker keren untuk chat

📚 **Ketik /help untuk tutorial lengkap cara penggunaan**

🎯 **Quick Start:**
• Kirim NIK 16 digit untuk cek data
• Kirim link video untuk download
• Kirim foto untuk edit dengan AI
• Ketik "search [kata kunci]" untuk cari video

⚡ **Bot siap melayani Anda 24/7!**`, { parse_mode: 'Markdown' }));

// Help command
bot.help((ctx) => ctx.reply(\`📚 **PANDUAN LENGKAP JVG DOWNLOAD BOT**

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🆔 **1. CEK NIK INDONESIA**
📝 **Cara:** Kirim NIK 16 digit langsung
📋 **Contoh:** \\\`3204110609970001\\\`
⏱️ **Proses:** Instant validation & parsing
📊 **Info:** Provinsi, Kab/Kota, Kecamatan, Kodepos, Gender, Tanggal Lahir

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📹 **2. DOWNLOAD VIDEO**
📝 **Cara:** Kirim link video langsung
🌐 **Platform:** TikTok, YouTube, Instagram, Facebook
📋 **Contoh:**
   • \\\`https://tiktok.com/@user/video/123\\\`
   • \\\`https://youtu.be/dQw4w9WgXcQ\\\`
   • \\\`https://instagram.com/p/ABC123\\\`
⏱️ **Proses:** 30-60 detik tergantung ukuran file
📱 **Format:** MP4 berkualitas tinggi

🚀 **Selamat menggunakan JVG DOWNLOAD BOT!**\`, { parse_mode: 'Markdown' }));
