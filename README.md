# JVG DOWNLOAD BOT

Bot Telegram multifungsi dengan fitur lengkap yang dapat:
1. **Cek NIK** - Validasi dan parsing informasi NIK Indonesia
2. **Download Video** - Download video dari TikTok, YouTube, Instagram, dan Facebook
3. **Download MP3** - Download audio MP3 dari YouTube
4. **Search YouTube** - Cari video YouTube tanpa perlu link
5. **AI Background Removal** - Hapus background foto dengan AI
6. **AI Image Enhancement** - Tingkatkan kualitas foto dengan AI
7. **Sticker Maker** - Buat sticker dari foto dengan mudah

## 🚀 Setup

### 1. Install Dependencies
```bash
npm install telegraf nik-parser abot-scraper axios wa-sticker-formatter
```

### 2. Setup Environment Variables
1. Edit file `.env` dan masukkan token bot Telegram Anda:
```
BOT_TOKEN=your_bot_token_here
```

### 3. Dapatkan Token Bot
1. Cha<PERSON> dengan [@<PERSON><PERSON><PERSON>ather](https://t.me/botfather) di Telegram
2. <PERSON><PERSON><PERSON> command `/newbot` untuk membuat bot baru
3. <PERSON><PERSON><PERSON> instruksi dan dapatkan token
4. Masukkan token ke file `.env`

### 4. Jalankan Bot
```bash
node index.js
```

## 📋 Fitur Lengkap

### 1. 🆔 Cek NIK
Kirim NIK 16 digit ke bot, contoh:
```
3204110609970001
```

Bot akan membalas dengan informasi:
- 🏛️ Provinsi
- 🏘️ Kabupaten/Kota  
- 🏢 Kecamatan
- 📮 Kodepos
- 👤 Jenis Kelamin
- 🎂 Tanggal Lahir
- 🔢 Uniqcode

### 2. 📹 Download Video
Kirim link video dari platform yang didukung:
- **TikTok**: `https://tiktok.com/...`
- **YouTube**: `https://youtube.com/...` atau `https://youtu.be/...`
- **Instagram**: `https://instagram.com/...`
- **Facebook**: `https://facebook.com/...`

Bot akan mendownload dan mengirim video langsung ke chat.

### 3. 🎵 Download MP3 dari YouTube
Untuk download audio saja dari YouTube:
```
mp3 https://youtu.be/abc123
mp3 https://youtube.com/watch?v=abc123
```

Bot akan mengirim file audio MP3 berkualitas tinggi.

### 4. 🔍 Search YouTube
Cari video di YouTube tanpa perlu link:
```
search phonk music
search tutorial programming
search lagu indonesia
```

Bot akan menampilkan 5 hasil teratas dengan:
- Judul video
- Channel/Creator
- Durasi
- Link untuk download

### 5. 🎨 AI Background Removal
1. **Kirim foto** ke bot
2. **Ketik** `remove bg`
3. Bot akan menghapus background dengan AI dan mengirim hasil

### 6. ✨ AI Image Enhancement
1. **Kirim foto** ke bot
2. **Pilih versi AI**:
   - Ketik `enhance` untuk AI V1
   - Ketik `enhance v2` untuk AI V2
3. Bot akan meningkatkan kualitas foto dan mengirim hasil

### 7. 🎭 Sticker Maker
1. **Kirim foto** ke bot
2. **Ketik** `sticker`
3. Bot akan membuat sticker WebP dan mengirim ke chat

**Fitur Sticker:**
- Format WebP berkualitas tinggi
- Pack name: "JVG DOWNLOAD BOT"
- Author: "Telegram Bot"
- Kategori: 😀🎉
- Auto cleanup file temporary

## 💡 Tips Penggunaan

### Contoh Lengkap:
```
# Cek NIK
3204110609970001

# Download video
https://youtu.be/dQw4w9WgXcQ

# Download MP3
mp3 https://youtu.be/dQw4w9WgXcQ

# Search YouTube
search never gonna give you up

# AI Tools & Sticker (setelah kirim foto)
remove bg
enhance
enhance v2
sticker
```

### Rate Limiting:
- Cooldown 3 detik antar request per user
- Mencegah spam dan overload server

### File Management:
- File temporary otomatis terhapus
- Streaming download untuk efisiensi memory
- Support file video hingga ukuran besar

## 🔧 Perbaikan yang Telah Dilakukan

1. **✅ Keamanan Token** - Token disimpan di environment variable
2. **✅ File Management** - Cleanup otomatis dengan try-finally
3. **✅ Memory Usage** - Menggunakan streaming untuk download
4. **✅ Validasi URL** - Validasi URL yang lebih ketat
5. **✅ Error Handling** - Error handling yang robust sesuai format API
6. **✅ Rate Limiting** - Mencegah spam request
7. **✅ AI Integration** - Fitur AI untuk background removal dan enhancement
8. **✅ Search Feature** - Pencarian YouTube tanpa perlu link
9. **✅ MP3 Support** - Download audio dari YouTube
🔟 **✅ Sticker Maker** - Buat sticker WebP dari foto

## ⚠️ Catatan Penting

- Pastikan file `.env` tidak di-commit ke repository
- Bot memerlukan akses internet untuk download video dan AI processing
- Folder `temp/` akan dibuat otomatis untuk file sementara
- File sementara akan dihapus otomatis setelah dikirim
- AI tools memerlukan koneksi yang stabil

## 🐛 Troubleshooting

Jika bot tidak berjalan:
1. ✅ Pastikan token bot benar di file `.env`
2. ✅ Pastikan semua dependencies terinstall
3. ✅ Cek koneksi internet
4. ✅ Lihat log error di console
5. ✅ Pastikan bot tidak di-block oleh firewall

Jika download gagal:
1. ✅ Cek apakah link valid dan dapat diakses
2. ✅ Pastikan video tidak private/restricted
3. ✅ Coba lagi setelah beberapa saat

Jika AI tools tidak bekerja:
1. ✅ Pastikan foto tidak terlalu besar (max 20MB)
2. ✅ Pastikan format foto didukung (JPG, PNG)
3. ✅ Cek koneksi internet yang stabil

## 📦 Dependencies

- `telegraf` - Framework bot Telegram
- `nik-parser` - Parser dan validator NIK Indonesia
- `abot-scraper` - Scraper untuk download video dan AI tools
- `axios` - HTTP client untuk download file
- `wa-sticker-formatter` - Library untuk membuat sticker WebP

## 📄 License

MIT License - Bebas digunakan dan dimodifikasi.
