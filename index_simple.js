// JVG DOWNLOAD BOT - Bot Telegram dengan fitur lengkap
// Install: npm install telegraf nik-parser abot-scraper axios wa-sticker-formatter

const { Telegraf } = require('telegraf');
const { nikParser } = require('nik-parser');
const { Downloader, Search, Tools } = require('abot-scraper');
const { Sticker, StickerTypes } = require('wa-sticker-formatter');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Load environment variables
function loadEnv() {
  try {
    const envPath = path.join(__dirname, '.env');
    if (fs.existsSync(envPath)) {
      const envFile = fs.readFileSync(envPath, 'utf8');
      const lines = envFile.split('\n');
      lines.forEach(line => {
        const [key, value] = line.split('=');
        if (key && value) {
          process.env[key.trim()] = value.trim();
        }
      });
    }
  } catch (error) {
    console.warn('Warning: Could not load .env file');
  }
}

loadEnv();

const BOT_TOKEN = process.env.BOT_TOKEN || '7839973328:AAGk2MtpS3jj0lLJgxZWxG2lQxEqSgDn7mw';
const bot = new Telegraf(BOT_TOKEN);
const downloader = new Downloader();
const search = new Search();
const tools = new Tools();

// Rate limiting
const userLastRequest = new Map();

function checkRateLimit(userId) {
  const now = Date.now();
  const lastRequest = userLastRequest.get(userId);
  
  if (lastRequest && (now - lastRequest) < 3000) {
    return false;
  }
  
  userLastRequest.set(userId, now);
  return true;
}

// Bot start command
bot.start((ctx) => ctx.reply(`🚀 **Selamat Datang di JVG DOWNLOAD BOT!**

✨ **Bot All-in-One dengan 7 Fitur Canggih:**

🆔 **NIK Checker** - Validasi & info lengkap NIK Indonesia
📹 **Video Downloader** - TikTok, YouTube, Instagram, Facebook  
🎵 **MP3 Downloader** - Audio berkualitas dari YouTube
🔍 **YouTube Search** - Cari video tanpa ribet copy link
🎨 **AI Background Remover** - Hapus background otomatis
✨ **AI Image Enhancer** - Tingkatkan kualitas foto dengan AI
🎭 **Sticker Maker** - Buat sticker keren untuk chat

📚 **Ketik /help untuk tutorial lengkap cara penggunaan**

🎯 **Quick Start:**
• Kirim NIK 16 digit untuk cek data
• Kirim link video untuk download  
• Kirim foto untuk edit dengan AI
• Ketik "search [kata kunci]" untuk cari video

⚡ **Bot siap melayani Anda 24/7!**`, { parse_mode: 'Markdown' }));

// Help command
bot.help((ctx) => ctx.reply(`📚 **PANDUAN LENGKAP JVG DOWNLOAD BOT**

🆔 **1. CEK NIK** - Kirim NIK 16 digit langsung
📹 **2. DOWNLOAD VIDEO** - Kirim link TikTok/YouTube/Instagram/Facebook
🎵 **3. DOWNLOAD MP3** - Ketik "mp3 [link YouTube]"
🔍 **4. SEARCH YOUTUBE** - Ketik "search [kata kunci]"
🎨 **5. AI BACKGROUND REMOVER** - Kirim foto + ketik "remove bg"
✨ **6. AI IMAGE ENHANCER** - Kirim foto + ketik "enhance" atau "enhance v2"
🎭 **7. STICKER MAKER** - Kirim foto + ketik "sticker"

💡 **TIPS:**
• Rate limit: 3 detik antar request
• File otomatis terhapus setelah dikirim
• Combo workflow: Foto → AI Edit → Sticker

🚀 **Selamat menggunakan JVG DOWNLOAD BOT!**`, { parse_mode: 'Markdown' }));

// Handle photos
bot.on('photo', async (ctx) => {
  const userId = ctx.from.id;
  
  if (!checkRateLimit(userId)) {
    await ctx.reply('⏳ Mohon tunggu beberapa detik sebelum mengirim permintaan lagi.');
    return;
  }
  
  try {
    const photo = ctx.message.photo[ctx.message.photo.length - 1];
    const fileLink = await ctx.telegram.getFileLink(photo.file_id);
    const response = await axios.get(fileLink.href, { responseType: 'arraybuffer' });
    const photoBuffer = Buffer.from(response.data);
    
    ctx.session = ctx.session || {};
    ctx.session.photoBuffer = photoBuffer;
    
    await ctx.reply(`📸 **Foto berhasil diterima!**

🎯 **Pilih aksi:**
🎨 \`remove bg\` - Hapus background dengan AI
✨ \`enhance\` - Tingkatkan kualitas (AI V1)
⚡ \`enhance v2\` - Tingkatkan kualitas (AI V2)
🎭 \`sticker\` - Buat sticker WebP

💡 **Tips:** Bisa kombinasi! Contoh: enhance → sticker`, { parse_mode: 'Markdown' });
    
  } catch (error) {
    console.error('Photo error:', error);
    await ctx.reply('❌ Gagal memproses foto. Silakan coba lagi.');
  }
});

// Handle text messages
bot.on('text', async (ctx) => {
  const text = ctx.message.text.trim();
  const userId = ctx.from.id;
  
  if (!checkRateLimit(userId)) {
    await ctx.reply('⏳ Mohon tunggu beberapa detik sebelum mengirim permintaan lagi.');
    return;
  }
  
  try {
    // MP3 download
    if (text.toLowerCase().startsWith('mp3 ')) {
      const url = text.substring(4).trim();
      await ctx.reply('🎵 **Memproses audio MP3...**\n⏳ Mengekstrak audio berkualitas tinggi...');
      
      try {
        const result = await downloader.ytMp3Downloader(url);
        if (result.status === 200 && result.result && result.result.audio) {
          await ctx.replyWithAudio({ url: result.result.audio }, { caption: '🎵 Audio MP3 berhasil didownload!' });
        } else {
          await ctx.reply('❌ Gagal download MP3: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }
    
    // YouTube search
    if (text.toLowerCase().startsWith('search ')) {
      const query = text.substring(7).trim();
      await ctx.reply('🔍 **Mencari video di YouTube...**\n⏳ Menganalisis database YouTube...');
      
      try {
        const result = await search.ytSearch(query);
        if (result.status === 200 && result.result && Array.isArray(result.result)) {
          const videos = result.result.slice(0, 5);
          let message = `🎥 **Hasil Pencarian "${query}":**\n\n`;
          
          videos.forEach((video, index) => {
            message += `${index + 1}. **${video.title}**\n`;
            message += `   👤 ${video.channel}\n`;
            message += `   🔗 ${video.url}\n\n`;
          });
          
          await ctx.reply(message, { parse_mode: 'Markdown' });
        } else {
          await ctx.reply('❌ Tidak ditemukan video untuk pencarian tersebut');
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }
    
    // AI commands
    if (text.toLowerCase() === 'remove bg') {
      if (!ctx.session || !ctx.session.photoBuffer) {
        await ctx.reply('❌ Tidak ada foto. Kirim foto terlebih dahulu.');
        return;
      }
      
      await ctx.reply('🎨 **AI Background Remover aktif!**\n⏳ Menghapus background...');
      try {
        const result = await tools.removeBackground(ctx.session.photoBuffer);
        if (result.status === 200 && result.result && result.result.image) {
          await ctx.replyWithPhoto({ url: result.result.image }, { caption: '✨ Background berhasil dihapus!' });
        } else {
          await ctx.reply('❌ Gagal menghapus background: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }
    
    if (text.toLowerCase() === 'enhance' || text.toLowerCase() === 'enhance v1') {
      if (!ctx.session || !ctx.session.photoBuffer) {
        await ctx.reply('❌ Tidak ada foto. Kirim foto terlebih dahulu.');
        return;
      }
      
      await ctx.reply('✨ **AI Image Enhancer V1 aktif!**\n⏳ Meningkatkan kualitas foto...');
      try {
        const result = await tools.reminiV1(ctx.session.photoBuffer);
        if (result.status === 200 && result.result && result.result.image) {
          await ctx.replyWithPhoto({ url: result.result.image }, { caption: '✨ Kualitas foto berhasil ditingkatkan!' });
        } else {
          await ctx.reply('❌ Gagal meningkatkan kualitas: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }
    
    if (text.toLowerCase() === 'enhance v2') {
      if (!ctx.session || !ctx.session.photoBuffer) {
        await ctx.reply('❌ Tidak ada foto. Kirim foto terlebih dahulu.');
        return;
      }
      
      await ctx.reply('⚡ **AI Image Enhancer V2 aktif!**\n⏳ Meningkatkan kualitas foto...');
      try {
        const result = await tools.reminiV2(ctx.session.photoBuffer);
        if (result.status === 200 && result.result && result.result.image) {
          await ctx.replyWithPhoto({ url: result.result.image }, { caption: '⚡ Kualitas foto berhasil ditingkatkan dengan AI V2!' });
        } else {
          await ctx.reply('❌ Gagal meningkatkan kualitas: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }
    
    if (text.toLowerCase() === 'sticker') {
      if (!ctx.session || !ctx.session.photoBuffer) {
        await ctx.reply('❌ Tidak ada foto. Kirim foto terlebih dahulu.');
        return;
      }
      
      await ctx.reply('🎭 **Sticker Maker aktif!**\n⏳ Membuat sticker WebP...');
      try {
        const sticker = new Sticker(ctx.session.photoBuffer, {
          pack: 'JVG DOWNLOAD BOT',
          author: 'Telegram Bot',
          type: StickerTypes.FULL,
          categories: ['😀', '🎉'],
          quality: 75
        });
        
        const stickerBuffer = await sticker.toBuffer();
        await ctx.replyWithSticker({ source: stickerBuffer }, { caption: '🎉 Sticker berhasil dibuat!' });
      } catch (e) {
        await ctx.reply('❌ Gagal membuat sticker: ' + e.message);
      }
      return;
    }

    // Video downloads
    if (text.includes('tiktok.com')) {
      await ctx.reply('📱 **TikTok Downloader aktif!**\n⏳ Mengekstrak video tanpa watermark...');
      try {
        const result = await downloader.tiktokDownloader(text);
        if (result.status === 200 && result.result && result.result.video) {
          await ctx.replyWithVideo({ url: result.result.video }, { caption: '📱 Video TikTok berhasil didownload!' });
        } else {
          await ctx.reply('❌ Gagal download TikTok: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }

    if (text.includes('youtu.be') || text.includes('youtube.com')) {
      await ctx.reply('🎥 **YouTube Downloader aktif!**\n⏳ Mengunduh video kualitas terbaik...');
      try {
        const result = await downloader.youtubeDownloader(text);
        if (result.status === 200 && result.result && result.result.video) {
          await ctx.replyWithVideo({ url: result.result.video }, { caption: '🎥 Video YouTube berhasil didownload!' });
        } else {
          await ctx.reply('❌ Gagal download YouTube: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }

    if (text.includes('instagram.com')) {
      await ctx.reply('📷 **Instagram Downloader aktif!**\n⏳ Mengunduh konten Instagram...');
      try {
        const result = await downloader.instagramDownloader(text);
        if (result.status === 200 && result.result && result.result.video) {
          await ctx.replyWithVideo({ url: result.result.video }, { caption: '📷 Konten Instagram berhasil didownload!' });
        } else {
          await ctx.reply('❌ Gagal download Instagram: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }

    if (text.includes('facebook.com')) {
      await ctx.reply('📘 **Facebook Downloader aktif!**\n⏳ Mengekstrak video Facebook...');
      try {
        const result = await downloader.facebookDownloader(text);
        if (result.status === 200 && result.result && result.result.video) {
          await ctx.replyWithVideo({ url: result.result.video }, { caption: '📘 Video Facebook berhasil didownload!' });
        } else {
          await ctx.reply('❌ Gagal download Facebook: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }

    // NIK check
    if (/^\d{16}$/.test(text)) {
      try {
        const nik = nikParser(text);
        if (!nik.isValid()) {
          await ctx.reply('❌ NIK tidak valid.');
          return;
        }

        const hasil = `📋 **Informasi NIK:**
NIK: \`${text}\`
🏛️ Provinsi: ${nik.province()}
🏘️ Kab/Kota: ${nik.kabupatenKota()}
🏢 Kecamatan: ${nik.kecamatan()}
📮 Kodepos: ${nik.kodepos()}
👤 Jenis Kelamin: ${nik.kelamin()}
🎂 Tanggal Lahir: ${nik.lahir().toISOString().slice(0,10)}
🔢 Uniqcode: ${nik.uniqcode()}`;

        await ctx.reply(hasil, { parse_mode: 'Markdown' });
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan saat memproses NIK.');
      }
      return;
    }

    // Default help
    await ctx.reply(`❓ **Perintah tidak dikenali!**

🎯 **Fitur yang tersedia:**
🆔 **Cek NIK**: Kirim NIK 16 digit
📹 **Download Video**: Kirim link video
🎵 **Download MP3**: \`mp3 [link YouTube]\`
🔍 **Search YouTube**: \`search [kata kunci]\`
🎨 **AI Tools**: Kirim foto + perintah AI
🎭 **Sticker**: Kirim foto + \`sticker\`

📚 **Ketik /help untuk tutorial lengkap**
🏠 **Ketik /start untuk menu utama**

⚡ **Bot siap melayani 24/7!**`, { parse_mode: 'Markdown' });

  } catch (error) {
    console.error('Error:', error);
    await ctx.reply('❌ Terjadi kesalahan. Silakan coba lagi.');
  }
});

// Launch bot
bot.launch();

// Graceful stop
process.once('SIGINT', () => bot.stop('SIGINT'));
process.once('SIGTERM', () => bot.stop('SIGTERM'));

console.log('🤖 JVG DOWNLOAD BOT berhasil dijalankan!');
console.log('📝 Bot siap menerima pesan...');
console.log('🔧 Tekan Ctrl+C untuk menghentikan bot');
