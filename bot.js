// JVG DOWNLOAD BOT - Bot Telegram dengan fitur lengkap
// Install: npm install telegraf nik-parser abot-scraper axios wa-sticker-formatter

const { Telegraf } = require('telegraf');
const { nikParser } = require('nik-parser');
const { Downloader, Search, Tools } = require('abot-scraper');
const { Sticker, StickerTypes } = require('wa-sticker-formatter');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Load environment variables
function loadEnv() {
  try {
    const envPath = path.join(__dirname, '.env');
    if (fs.existsSync(envPath)) {
      const envFile = fs.readFileSync(envPath, 'utf8');
      const lines = envFile.split('\n');
      lines.forEach(line => {
        const [key, value] = line.split('=');
        if (key && value) {
          process.env[key.trim()] = value.trim();
        }
      });
    }
  } catch (error) {
    console.warn('Warning: Could not load .env file');
  }
}

loadEnv();

const BOT_TOKEN = process.env.BOT_TOKEN || '7839973328:AAGk2MtpS3jj0lLJgxZWxG2lQxEqSgDn7mw';
const bot = new Telegraf(BOT_TOKEN);
const downloader = new Downloader();
const search = new Search();
const tools = new Tools();

// Rate limiting dan foto caching
const userLastRequest = new Map();
const userPhotoCache = new Map(); // Cache foto user

// Check rate limiting
function checkRateLimit(userId) {
    const now = Date.now();
    const lastRequest = userLastRequest.get(userId);

    if (lastRequest && (now - lastRequest) < 3000) {
        return false;
    }

    userLastRequest.set(userId, now);
    return true;
}

// Simpan foto user ke cache
function cacheUserPhoto(userId, photoData) {
    userPhotoCache.set(userId, {
        ...photoData,
        timestamp: Date.now()
    });

    // Auto cleanup cache setelah 10 menit
    setTimeout(() => {
        userPhotoCache.delete(userId);
    }, 10 * 60 * 1000);
}

// Ambil foto dari cache
function getCachedPhoto(userId) {
    const cached = userPhotoCache.get(userId);
    if (cached && (Date.now() - cached.timestamp) < 10 * 60 * 1000) {
        return cached;
    }
    return null;
}

// Download and send video function with validation
async function downloadAndSendVideo(ctx, videoUrl, platform) {
    let filePath = null;

    try {
        // Validate URL format
        const urlPattern = /^https?:\/\/.+/;
        if (!urlPattern.test(videoUrl)) {
            throw new Error('URL tidak valid');
        }

        // Create temp directory
        const tempDir = path.join(__dirname, 'temp');
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, {recursive: true});
        }

        const fileName = `video_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.mp4`;
        filePath = path.join(tempDir, fileName);

        // Download with streaming and validation
        const response = await axios({
            method: 'GET',
            url: videoUrl,
            responseType: 'stream',
            timeout: 60000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        // Check if response is valid
        if (!response.data) {
            throw new Error('Tidak ada data video yang diterima');
        }

        const writer = fs.createWriteStream(filePath);
        response.data.pipe(writer);

        await new Promise((resolve, reject) => {
            writer.on('finish', resolve);
            writer.on('error', reject);
            response.data.on('error', reject);
        });

        // Validate file exists and has content
        if (!fs.existsSync(filePath)) {
            throw new Error('File video tidak berhasil disimpan');
        }

        const stats = fs.statSync(filePath);
        if (stats.size === 0) {
            throw new Error('File video kosong atau corrupt');
        }

        // Send video with file source
        await ctx.replyWithVideo({
            source: filePath
        }, {
            caption: `📹 Video ${platform} berhasil didownload!`
        });

        console.log(`✅ ${platform} video sent successfully, size: ${stats.size} bytes`);

    } catch (error) {
        console.error(`${platform} download error:`, error);

        // Try fallback method with direct URL if file method fails
        if (error.message.includes('file must be non-empty') || error.message.includes('kosong')) {
            try {
                await ctx.reply('🔄 Mencoba metode alternatif...');
                await ctx.replyWithVideo({ url: videoUrl }, {
                    caption: `📹 Video ${platform} berhasil didownload!`
                });
                console.log(`✅ ${platform} video sent via URL fallback`);
            } catch (fallbackError) {
                console.error(`${platform} fallback error:`, fallbackError);
                await ctx.reply(`❌ Gagal mengirim video ${platform}: ${error.message}`);
            }
        } else {
            await ctx.reply(`❌ Gagal mengirim video ${platform}: ${error.message}`);
        }
    } finally {
        // Clean up temporary file
        if (filePath && fs.existsSync(filePath)) {
            try {
                fs.unlinkSync(filePath);
                console.log(`🗑️ Temporary file deleted: ${filePath}`);
            } catch (cleanupError) {
                console.error('Error deleting temporary file:', cleanupError);
            }
        }
    }
}



// Bot start command
bot.start((ctx) => ctx.reply(`🚀 **Selamat Datang di JVG DOWNLOAD BOT!**

✨ **Bot All-in-One dengan 7 Fitur Canggih:**

🆔 **NIK Checker** - Validasi & info lengkap NIK Indonesia
📹 **Video Downloader** - TikTok, YouTube, Instagram, Facebook  
🎵 **MP3 Downloader** - Audio berkualitas dari YouTube
🔍 **YouTube Search** - Cari video tanpa ribet copy link
🎨 **AI Background Remover** - Hapus background otomatis
✨ **AI Image Enhancer** - Tingkatkan kualitas foto dengan AI
🎭 **Sticker Maker** - Buat sticker keren untuk chat

📚 **Ketik /help untuk tutorial lengkap cara penggunaan**

🎯 **Quick Start:**
• Kirim NIK 16 digit untuk cek data
• Kirim link video untuk download  
• Kirim foto untuk edit dengan AI
• Ketik "search [kata kunci]" untuk cari video

⚡ **Bot siap melayani Anda 24/7!**`, { parse_mode: 'Markdown' }));

// Help command
bot.help((ctx) => ctx.reply(`📚 **PANDUAN LENGKAP JVG DOWNLOAD BOT**

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🆔 **1. CEK NIK INDONESIA**
📝 **Cara:** Kirim NIK 16 digit langsung
📋 **Contoh:** \`3204110609970001\`
⏱️ **Proses:** Instant validation & parsing
📊 **Info:** Provinsi, Kab/Kota, Kecamatan, Kodepos, Gender, Tanggal Lahir

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📹 **2. DOWNLOAD VIDEO**
📝 **Cara:** Kirim link video langsung
🌐 **Platform:** TikTok, YouTube, Instagram, Facebook
📋 **Contoh:** 
   • \`https://tiktok.com/@user/video/123\`
   • \`https://youtu.be/dQw4w9WgXcQ\`
   • \`https://instagram.com/p/ABC123\`
⏱️ **Proses:** 30-60 detik tergantung ukuran file
📱 **Format:** MP4 berkualitas tinggi

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🎵 **3. DOWNLOAD MP3 YOUTUBE**
📝 **Cara:** Ketik "mp3" + spasi + link YouTube
📋 **Contoh:** \`mp3 https://youtu.be/dQw4w9WgXcQ\`
⏱️ **Proses:** 20-40 detik
🎧 **Kualitas:** Audio HD tanpa video

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🔍 **4. SEARCH YOUTUBE**
📝 **Cara:** Ketik "search" + spasi + kata kunci
📋 **Contoh:** 
   • \`search lagu indonesia terbaru\`
   • \`search tutorial programming\`
   • \`search phonk music\`
⏱️ **Proses:** 5-10 detik
📊 **Hasil:** 5 video teratas dengan link download

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🎨 **5. AI BACKGROUND REMOVER**
📝 **Cara:** 
   1️⃣ Kirim foto ke bot
   2️⃣ Ketik \`remove bg\`
⏱️ **Proses:** 15-30 detik dengan AI
✨ **Hasil:** Background transparan otomatis
📸 **Support:** JPG, PNG (max 20MB)

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

✨ **6. AI IMAGE ENHANCER**
📝 **Cara:** 
   1️⃣ Kirim foto ke bot
   2️⃣ Ketik \`enhance\` (AI V1) atau \`enhance v2\` (AI V2)
⏱️ **Proses:** 20-45 detik dengan AI
🔧 **Hasil:** Kualitas foto meningkat drastis
📸 **Support:** JPG, PNG (max 20MB)

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🎭 **7. STICKER MAKER**
📝 **Cara:** 
   1️⃣ Kirim foto ke bot
   2️⃣ Ketik \`sticker\`
⏱️ **Proses:** 10-20 detik
🎨 **Hasil:** Sticker WebP siap pakai
📱 **Format:** Compatible dengan semua chat app

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

💡 **TIPS & TRICKS:**
🔄 **Combo Workflow:** Foto → AI Edit → Sticker
⚡ **Rate Limit:** 3 detik cooldown antar request
🗂️ **File Management:** Auto cleanup temporary files
🛡️ **Privacy:** File dihapus otomatis setelah dikirim

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

❓ **BUTUH BANTUAN?**
📞 **Support:** Kirim pesan error untuk troubleshooting
🔄 **Update:** Bot diupdate berkala dengan fitur baru
⭐ **Rating:** Berikan feedback untuk improvement

🚀 **Selamat menggunakan JVG DOWNLOAD BOT!**`, { parse_mode: 'Markdown' }));

// Handle photos for AI processing
bot.on('photo', async (ctx) => {
  const userId = ctx.from.id;
  
  if (!checkRateLimit(userId)) {
    await ctx.reply('⏳ Mohon tunggu beberapa detik sebelum mengirim permintaan lagi.');
    return;
  }
  
  try {
    // Get the largest photo
    const photo = ctx.message.photo[ctx.message.photo.length - 1];
    const fileLink = await ctx.telegram.getFileLink(photo.file_id);

    // Download photo
    const response = await axios.get(fileLink.href, { responseType: 'arraybuffer' });
    const photoBuffer = Buffer.from(response.data);

    // Cache foto user
    cacheUserPhoto(userId, {
      buffer: photoBuffer,
      fileId: photo.file_id,
      fileLink: fileLink.href
    });

    await ctx.reply(`📸 **Foto berhasil diterima dan tersimpan!**

🎯 **Pilih aksi yang ingin dilakukan:**

🎨 \`remove bg\` - Hapus background otomatis dengan AI
✨ \`enhance\` - Tingkatkan kualitas dengan AI V1 (cepat)
⚡ \`enhance v2\` - Tingkatkan kualitas dengan AI V2 (premium)
🎭 \`sticker\` - Buat sticker WebP keren untuk chat

💡 **Tips:** Anda bisa kombinasi! Contoh: enhance → sticker
🔄 **Ganti foto:** Kirim foto baru untuk mengganti foto yang akan diproses`, { parse_mode: 'Markdown' });
    
  } catch (error) {
    console.error('Photo processing error:', error);
    await ctx.reply('❌ Gagal memproses foto. Silakan coba lagi.');
  }
});

// Handle text messages
bot.on('text', async (ctx) => {
  const text = ctx.message.text.trim();
  const userId = ctx.from.id;
  
  if (!checkRateLimit(userId)) {
    await ctx.reply('⏳ Mohon tunggu beberapa detik sebelum mengirim permintaan lagi.');
    return;
  }
  
  try {
    // Handle MP3 download command
    if (text.toLowerCase().startsWith('mp3 ')) {
      const url = text.substring(4).trim();
      await ctx.reply('🎵 **Memproses audio MP3...**\n⏳ Mengekstrak audio berkualitas tinggi dari YouTube...');
      
      try {
        const result = await downloader.ytMp3Downloader(url);
        if (result.status === 200 && result.result && result.result.audio) {
          await ctx.replyWithAudio({ url: result.result.audio }, { caption: `🎵 ${result.result.title || 'Audio MP3'} berhasil didownload` });
        } else {
          await ctx.reply('❌ Gagal download MP3: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }
    
    // Handle search command
    if (text.toLowerCase().startsWith('search ')) {
      const query = text.substring(7).trim();
      if (query.length < 2) {
        await ctx.reply('❌ Kata kunci pencarian terlalu pendek. Minimal 2 karakter.');
        return;
      }
      
      await ctx.reply('🔍 **Mencari video di YouTube...**\n⏳ Menganalisis database YouTube untuk hasil terbaik...');
      
      try {
        const result = await search.ytSearch(query);
        if (result.status === 200 && result.result && Array.isArray(result.result)) {
          const videos = result.result.slice(0, 5);
          
          if (videos.length === 0) {
            await ctx.reply('❌ Tidak ditemukan video untuk pencarian tersebut');
            return;
          }
          
          let message = `🎥 **Hasil Pencarian YouTube untuk "${query}":**\n\n`;
          
          videos.forEach((video, index) => {
            message += `${index + 1}. **${video.title}**\n`;
            message += `   👤 ${video.channel}\n`;
            message += `   ⏱️ ${video.duration}\n`;
            message += `   🔗 ${video.url}\n\n`;
          });
          
          message += `💡 **Tip:** Kirim link video untuk download atau ketik "mp3 [link]" untuk download audio saja`;
          
          await ctx.reply(message, { parse_mode: 'Markdown' });
        } else {
          await ctx.reply('❌ Gagal mencari video: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }

    // Handle AI commands for photos
    if (text.toLowerCase() === 'remove bg') {
      const cachedPhoto = getCachedPhoto(userId);
      if (!cachedPhoto) {
        await ctx.reply('❌ Tidak ada foto yang tersimpan. Kirim foto terlebih dahulu, lalu ketik `remove bg`.');
        return;
      }

      await ctx.reply('🎨 **AI Background Remover aktif!**\n⏳ Menganalisis objek dan menghapus background secara otomatis...');

      try {
        const result = await tools.removeBackground(cachedPhoto.buffer);
        if (result.status === 200 && result.result && result.result.image) {
          await ctx.replyWithPhoto({ url: result.result.image }, { caption: '✨ Background berhasil dihapus dengan AI!' });
        } else {
          await ctx.reply('❌ Gagal menghapus background: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }

    if (text.toLowerCase() === 'enhance' || text.toLowerCase() === 'enhance v1') {
      const cachedPhoto = getCachedPhoto(userId);
      if (!cachedPhoto) {
        await ctx.reply('❌ Tidak ada foto yang tersimpan. Kirim foto terlebih dahulu, lalu ketik `enhance`.');
        return;
      }

      await ctx.reply('✨ **AI Image Enhancer V1 aktif!**\n⏳ Menganalisis dan meningkatkan kualitas foto dengan teknologi AI...');

      try {
        const result = await tools.reminiV1(cachedPhoto.buffer);
        if (result.status === 200 && result.result && result.result.image) {
          await ctx.replyWithPhoto({ url: result.result.image }, { caption: '✨ Kualitas gambar berhasil ditingkatkan dengan AI V1!' });
        } else {
          await ctx.reply('❌ Gagal meningkatkan kualitas gambar: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }

    if (text.toLowerCase() === 'enhance v2') {
      const cachedPhoto = getCachedPhoto(userId);
      if (!cachedPhoto) {
        await ctx.reply('❌ Tidak ada foto yang tersimpan. Kirim foto terlebih dahulu, lalu ketik `enhance v2`.');
        return;
      }

      await ctx.reply('⚡ **AI Image Enhancer V2 aktif!**\n⏳ Menganalisis dan meningkatkan kualitas foto dengan teknologi AI...');

      try {
        const result = await tools.reminiV2(cachedPhoto.buffer);
        if (result.status === 200 && result.result && result.result.image) {
          await ctx.replyWithPhoto({ url: result.result.image }, { caption: '✨ Kualitas gambar berhasil ditingkatkan dengan AI V2!' });
        } else {
          await ctx.reply('❌ Gagal meningkatkan kualitas gambar: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }

    if (text.toLowerCase() === 'sticker') {
      const cachedPhoto = getCachedPhoto(userId);
      if (!cachedPhoto) {
        await ctx.reply('❌ Tidak ada foto yang tersimpan. Kirim foto terlebih dahulu, lalu ketik `sticker`.');
        return;
      }

      await ctx.reply('🎭 **Sticker Maker aktif!**\n⏳ Mengkonversi foto menjadi sticker WebP berkualitas tinggi...');

      try {
        const sticker = new Sticker(cachedPhoto.buffer, {
          pack: 'JVG DOWNLOAD BOT',
          author: 'Telegram Bot',
          type: StickerTypes.FULL,
          categories: ['😀', '🎉'],
          quality: 75
        });

        const stickerBuffer = await sticker.toBuffer();
        await ctx.replyWithSticker({ source: stickerBuffer }, { caption: '🎉 Sticker berhasil dibuat!' });
      } catch (e) {
        await ctx.reply('❌ Gagal membuat sticker: ' + e.message);
      }
      return;
    }

    // Handle video download links
    if (text.includes('tiktok.com')) {
      await ctx.reply('📱 **TikTok Downloader aktif!**\n⏳ Mengekstrak video berkualitas tinggi tanpa watermark...');
      try {
        const result = await downloader.tiktokDownloader(text);
        if (result.status === 200 && result.result && result.result.video) {
          await ctx.reply('✅ **Download berhasil!** Mengirim video TikTok tanpa watermark...');
          await downloadAndSendVideo(ctx, result.result.video, 'TikTok');
        } else {
          await ctx.reply('❌ Gagal download video TikTok: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        console.error('TikTok error:', e);
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }

    if (text.includes('youtu.be') || text.includes('youtube.com')) {
      await ctx.reply('🎥 **YouTube Downloader aktif!**\n⏳ Mengunduh video dalam kualitas terbaik yang tersedia...');
      try {
        const result = await downloader.youtubeDownloader(text);
        if (result.status === 200 && result.result && result.result.video) {
          await ctx.reply('✅ **Download berhasil!** Mengirim video YouTube kualitas HD...');
          await downloadAndSendVideo(ctx, result.result.video, 'YouTube');
        } else {
          await ctx.reply('❌ Gagal download video YouTube: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        console.error('YouTube error:', e);
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }

    if (text.includes('instagram.com')) {
      await ctx.reply('📷 **Instagram Downloader aktif!**\n⏳ Mengunduh konten Instagram dalam resolusi original...');
      try {
        const result = await downloader.instagramDownloader(text);
        if (result.status === 200 && result.result && result.result.video) {
          await ctx.reply('✅ **Download berhasil!** Mengirim konten Instagram resolusi original...');
          await downloadAndSendVideo(ctx, result.result.video, 'Instagram');
        } else {
          await ctx.reply('❌ Gagal download video Instagram: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        console.error('Instagram error:', e);
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }

    if (text.includes('facebook.com')) {
      await ctx.reply('📘 **Facebook Downloader aktif!**\n⏳ Mengekstrak video Facebook dalam kualitas HD...');
      try {
        const result = await downloader.facebookDownloader(text);
        if (result.status === 200 && result.result && result.result.video) {
          await ctx.reply('✅ **Download berhasil!** Mengirim video Facebook kualitas HD...');
          await downloadAndSendVideo(ctx, result.result.video, 'Facebook');
        } else {
          await ctx.reply('❌ Gagal download video Facebook: ' + (result.msg || 'Unknown error'));
        }
      } catch (e) {
        console.error('Facebook error:', e);
        await ctx.reply('❌ Terjadi kesalahan: ' + e.message);
      }
      return;
    }

    // Handle NIK check
    if (/^\d{16}$/.test(text)) {
      try {
        const nik = nikParser(text);
        if (!nik.isValid()) {
          await ctx.reply('❌ NIK tidak valid.');
          return;
        }

        const hasil = `📋 **Informasi NIK:**
NIK: \`${text}\`
🏛️ Provinsi: ${nik.province()}
🏘️ Kab/Kota: ${nik.kabupatenKota()}
🏢 Kecamatan: ${nik.kecamatan()}
📮 Kodepos: ${nik.kodepos()}
👤 Jenis Kelamin: ${nik.kelamin()}
🎂 Tanggal Lahir: ${nik.lahir().toISOString().slice(0,10)}
🔢 Uniqcode: ${nik.uniqcode()}`;

        await ctx.reply(hasil, { parse_mode: 'Markdown' });
      } catch (e) {
        console.error('NIK error:', e);
        await ctx.reply('❌ Terjadi kesalahan saat memproses NIK.');
      }
      return;
    }

    // Default help message
    await ctx.reply(`❓ **Perintah tidak dikenali!**

🎯 **Fitur yang tersedia:**

🆔 **Cek NIK**: Kirim NIK 16 digit langsung
📹 **Download Video**: Kirim link TikTok/YouTube/Instagram/Facebook
🎵 **Download MP3**: \`mp3 [link YouTube]\`
🔍 **Search YouTube**: \`search [kata kunci]\`
🎨 **AI Background Remover**: Kirim foto → ketik \`remove bg\`
✨ **AI Image Enhancer**: Kirim foto → ketik \`enhance\` atau \`enhance v2\`
🎭 **Sticker Maker**: Kirim foto → ketik \`sticker\`

💡 **Workflow AI:** Kirim foto dulu, lalu pilih aksi yang diinginkan!

📚 **Ketik /help untuk tutorial lengkap**
🏠 **Ketik /start untuk menu utama**

⚡ **Bot siap melayani 24/7!**`, { parse_mode: 'Markdown' });

  } catch (error) {
    console.error('Text processing error:', error);
    await ctx.reply('❌ Terjadi kesalahan saat memproses pesan. Silakan coba lagi.');
  }
});

// Launch bot
bot.launch();

// Enable graceful stop
process.once('SIGINT', () => bot.stop('SIGINT'));
process.once('SIGTERM', () => bot.stop('SIGTERM'));

console.log('🤖 JVG DOWNLOAD BOT berhasil dijalankan!');
console.log('📝 Bot siap menerima pesan...');
console.log('🔧 Tekan Ctrl+C untuk menghentikan bot');
