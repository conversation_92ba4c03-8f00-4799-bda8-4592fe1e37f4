// Script untuk menjalankan bot dengan debug logging yang lebih baik
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Buat direktori logs jika belum ada
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Buat direktori temp jika belum ada
const tempDir = path.join(__dirname, 'temp');
if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
}

// Fungsi untuk format timestamp
function getTimestamp() {
    return new Date().toISOString().replace('T', ' ').substr(0, 19);
}

// Fungsi untuk log dengan timestamp
function logWithTimestamp(message, type = 'INFO') {
    const timestamp = getTimestamp();
    const logMessage = `[${timestamp}] [${type}] ${message}`;
    console.log(logMessage);
    
    // Simpan ke file log
    const logFile = path.join(logsDir, `bot_${new Date().toISOString().substr(0, 10)}.log`);
    fs.appendFileSync(logFile, logMessage + '\n');
}

// Fungsi untuk menjalankan bot
function runBot() {
    logWithTimestamp('🚀 Starting Telegram Bot...', 'SYSTEM');
    
    // Spawn bot process
    const botProcess = spawn('node', ['bot.js'], {
        stdio: ['inherit', 'pipe', 'pipe'],
        cwd: __dirname
    });

    // Handle stdout
    botProcess.stdout.on('data', (data) => {
        const message = data.toString().trim();
        if (message) {
            logWithTimestamp(message, 'BOT');
        }
    });

    // Handle stderr
    botProcess.stderr.on('data', (data) => {
        const message = data.toString().trim();
        if (message) {
            logWithTimestamp(message, 'ERROR');
        }
    });

    // Handle process exit
    botProcess.on('close', (code) => {
        logWithTimestamp(`Bot process exited with code ${code}`, 'SYSTEM');
        
        if (code !== 0) {
            logWithTimestamp('Bot crashed! Restarting in 5 seconds...', 'SYSTEM');
            setTimeout(() => {
                runBot();
            }, 5000);
        }
    });

    // Handle process error
    botProcess.on('error', (error) => {
        logWithTimestamp(`Bot process error: ${error.message}`, 'ERROR');
    });

    // Graceful shutdown
    process.on('SIGINT', () => {
        logWithTimestamp('Received SIGINT, shutting down gracefully...', 'SYSTEM');
        botProcess.kill('SIGTERM');
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        logWithTimestamp('Received SIGTERM, shutting down gracefully...', 'SYSTEM');
        botProcess.kill('SIGTERM');
        process.exit(0);
    });

    return botProcess;
}

// Fungsi untuk monitoring sistem
function startSystemMonitoring() {
    setInterval(() => {
        const memUsage = process.memoryUsage();
        const memInfo = `Memory: RSS=${(memUsage.rss / 1024 / 1024).toFixed(2)}MB, Heap=${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`;
        logWithTimestamp(memInfo, 'MONITOR');
        
        // Cleanup old log files (keep only last 7 days)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        
        try {
            const logFiles = fs.readdirSync(logsDir);
            logFiles.forEach(file => {
                const filePath = path.join(logsDir, file);
                const stats = fs.statSync(filePath);
                if (stats.mtime < sevenDaysAgo) {
                    fs.unlinkSync(filePath);
                    logWithTimestamp(`Deleted old log file: ${file}`, 'CLEANUP');
                }
            });
        } catch (error) {
            logWithTimestamp(`Error cleaning up logs: ${error.message}`, 'ERROR');
        }
        
        // Cleanup temp files older than 1 hour
        try {
            const tempFiles = fs.readdirSync(tempDir);
            const oneHourAgo = new Date();
            oneHourAgo.setHours(oneHourAgo.getHours() - 1);
            
            tempFiles.forEach(file => {
                const filePath = path.join(tempDir, file);
                const stats = fs.statSync(filePath);
                if (stats.mtime < oneHourAgo) {
                    fs.unlinkSync(filePath);
                    logWithTimestamp(`Deleted old temp file: ${file}`, 'CLEANUP');
                }
            });
        } catch (error) {
            logWithTimestamp(`Error cleaning up temp files: ${error.message}`, 'ERROR');
        }
        
    }, 60000); // Every minute
}

// Main execution
if (require.main === module) {
    logWithTimestamp('🔧 Bot Debug Runner Started', 'SYSTEM');
    logWithTimestamp(`📁 Working directory: ${__dirname}`, 'SYSTEM');
    logWithTimestamp(`📝 Logs directory: ${logsDir}`, 'SYSTEM');
    logWithTimestamp(`🗂️ Temp directory: ${tempDir}`, 'SYSTEM');
    
    // Start system monitoring
    startSystemMonitoring();
    
    // Run bot
    runBot();
}

module.exports = { runBot, logWithTimestamp };
