// Test script untuk memverifikasi fungsi download video
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Fungsi test untuk download dan validasi video
async function testVideoDownload(videoUrl, platform) {
    console.log(`🧪 Testing ${platform} video download...`);
    let filePath = null;

    try {
        // Validate URL format
        const urlPattern = /^https?:\/\/.+/;
        if (!urlPattern.test(videoUrl)) {
            throw new Error('URL tidak valid');
        }

        // Create temp directory
        const tempDir = path.join(__dirname, 'temp');
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, {recursive: true});
        }

        const fileName = `test_video_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.mp4`;
        filePath = path.join(tempDir, fileName);

        console.log(`📥 Downloading from: ${videoUrl}`);
        console.log(`💾 Saving to: ${filePath}`);

        // Download with streaming and validation
        const response = await axios({
            method: 'GET',
            url: videoUrl,
            responseType: 'stream',
            timeout: 60000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        // Check if response is valid
        if (!response.data) {
            throw new Error('Tidak ada data video yang diterima');
        }

        console.log(`📊 Response status: ${response.status}`);
        console.log(`📋 Content-Type: ${response.headers['content-type']}`);
        console.log(`📏 Content-Length: ${response.headers['content-length'] || 'Unknown'}`);

        const writer = fs.createWriteStream(filePath);
        response.data.pipe(writer);

        await new Promise((resolve, reject) => {
            writer.on('finish', resolve);
            writer.on('error', reject);
            response.data.on('error', reject);
        });

        // Validate file exists and has content
        if (!fs.existsSync(filePath)) {
            throw new Error('File video tidak berhasil disimpan');
        }

        const stats = fs.statSync(filePath);
        if (stats.size === 0) {
            throw new Error('File video kosong atau corrupt');
        }

        console.log(`✅ ${platform} video downloaded successfully!`);
        console.log(`📊 File size: ${stats.size} bytes (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
        console.log(`📁 File path: ${filePath}`);

        return {
            success: true,
            filePath: filePath,
            fileSize: stats.size,
            platform: platform
        };

    } catch (error) {
        console.error(`❌ ${platform} download error:`, error.message);
        return {
            success: false,
            error: error.message,
            platform: platform
        };
    } finally {
        // Clean up temporary file
        if (filePath && fs.existsSync(filePath)) {
            try {
                fs.unlinkSync(filePath);
                console.log(`🗑️ Temporary file deleted: ${filePath}`);
            } catch (cleanupError) {
                console.error('Error deleting temporary file:', cleanupError);
            }
        }
    }
}

// Test dengan URL sample
async function runTests() {
    console.log('🚀 Starting video download tests...\n');

    // Test URLs (gunakan URL yang valid untuk testing)
    const testUrls = [
        {
            url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
            platform: 'Sample'
        }
    ];

    for (const test of testUrls) {
        await testVideoDownload(test.url, test.platform);
        console.log('─'.repeat(50));
    }

    console.log('✅ All tests completed!');
}

// Jalankan test jika file ini dieksekusi langsung
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { testVideoDownload };
