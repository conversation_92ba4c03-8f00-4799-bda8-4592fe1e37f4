# 🔧 Troubleshooting Video Download Issues

## ❌ Error: "Bad Request: file must be non-empty"

### 🔍 Penyebab Masalah:
1. **File video kosong atau corrupt** - URL video tidak valid atau expired
2. **Network timeout** - Koneksi terputus saat download
3. **API rate limiting** - Terlalu banyak request dalam waktu singkat
4. **Invalid video URL** - URL tidak mengarah ke file video yang valid
5. **Server downloader offline** - API scraper tidak merespon

### ✅ Solusi yang Diterapkan:

#### 1. **Validasi File Sebelum Kirim**
```javascript
// Cek apakah file exists dan tidak kosong
if (!fs.existsSync(filePath)) {
    throw new Error('File video tidak berhasil disimpan');
}

const stats = fs.statSync(filePath);
if (stats.size === 0) {
    throw new Error('File video kosong atau corrupt');
}
```

#### 2. **Fallback Method**
```javascript
// Jika file method gagal, coba direct URL
try {
    await ctx.replyWithVideo({ source: filePath });
} catch (error) {
    if (error.message.includes('file must be non-empty')) {
        await ctx.replyWithVideo({ url: videoUrl });
    }
}
```

#### 3. **Rate Limiting**
```javascript
// Cegah spam request
function checkRateLimit(userId) {
    const now = Date.now();
    const lastRequest = userLastRequest.get(userId);
    
    if (lastRequest && (now - lastRequest) < 3000) {
        return false;
    }
    
    userLastRequest.set(userId, now);
    return true;
}
```

#### 4. **Enhanced Error Handling**
```javascript
// Logging detail untuk debugging
console.log(`✅ ${platform} video sent successfully, size: ${stats.size} bytes`);
console.error(`${platform} download error:`, error);
```

#### 5. **File Cleanup**
```javascript
// Hapus file temporary setelah kirim
if (filePath && fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    console.log(`🗑️ Temporary file deleted: ${filePath}`);
}
```

## 🛠️ Cara Testing

### 1. **Test API Downloader**
```bash
node test_downloader_api.js
```

### 2. **Test Video Download Function**
```bash
node test_video_download.js
```

### 3. **Run Bot dengan Debug**
```bash
node run_bot_debug.js
```

## 📊 Monitoring & Logging

### Log Files:
- `logs/bot_YYYY-MM-DD.log` - Log harian bot
- `temp/` - File temporary video (auto cleanup)

### Log Format:
```
[2024-01-15 10:30:45] [BOT] ✅ TikTok video sent successfully, size: 2048576 bytes
[2024-01-15 10:30:46] [ERROR] TikTok download error: file must be non-empty
[2024-01-15 10:30:47] [BOT] 🔄 Mencoba metode alternatif...
```

## 🔄 Recovery Actions

### Jika Bot Crash:
1. **Auto Restart** - Script debug akan restart otomatis
2. **Log Analysis** - Cek log file untuk error pattern
3. **Memory Monitoring** - Monitor penggunaan memory

### Jika Download Gagal:
1. **Retry Mechanism** - Coba fallback method
2. **URL Validation** - Validasi format URL
3. **Platform Check** - Pastikan API scraper aktif

## 🚨 Common Issues & Solutions

### Issue 1: "URL tidak valid"
**Solusi:** Pastikan URL menggunakan format https:// dan domain yang benar

### Issue 2: "Tidak ada data video yang diterima"
**Solusi:** Cek koneksi internet dan status API scraper

### Issue 3: "File video kosong atau corrupt"
**Solusi:** URL video mungkin expired, minta user kirim URL baru

### Issue 4: "Timeout"
**Solusi:** Tingkatkan timeout atau coba lagi nanti

## 📈 Performance Optimization

### 1. **Memory Management**
- Auto cleanup file temporary
- Monitor memory usage
- Restart jika memory tinggi

### 2. **Network Optimization**
- Streaming download untuk file besar
- Retry mechanism untuk network error
- User-Agent header untuk bypass blocking

### 3. **Rate Limiting**
- 3 detik cooldown per user
- Prevent spam requests
- Queue system untuk high traffic

## 🔧 Configuration

### Environment Variables:
```bash
BOT_TOKEN=your_bot_token_here
NODE_ENV=production
LOG_LEVEL=info
```

### File Limits:
- Max file size: 50MB (Telegram limit)
- Timeout: 60 seconds
- Rate limit: 3 seconds per user

## 📞 Support

Jika masalah masih berlanjut:
1. Cek log files di folder `logs/`
2. Jalankan test script untuk isolasi masalah
3. Monitor memory dan CPU usage
4. Restart bot jika diperlukan

---

**Last Updated:** 2024-01-15
**Version:** 2.0.0
