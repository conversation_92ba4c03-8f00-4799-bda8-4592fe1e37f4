// Test script untuk AI tools
const { Tools } = require('abot-scraper');
const fs = require('fs');
const path = require('path');

const tools = new Tools();

// Test dengan sample image buffer
async function testAITools() {
    console.log('🧪 Testing AI Tools...\n');
    
    // Create a simple test image buffer (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
        0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x0F, 0x00, 0x00,
        0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8A, 0x8E, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);
    
    const base64Image = testImageBuffer.toString('base64');
    
    // Test 1: Upload Image
    console.log('📤 Testing uploadImage...');
    try {
        const uploadResult = await tools.uploadImage(testImageBuffer);
        console.log('✅ Upload result:', JSON.stringify(uploadResult, null, 2));
    } catch (e) {
        console.log('❌ Upload failed:', e.message);
    }
    
    console.log('\n' + '─'.repeat(50) + '\n');
    
    // Test 2: Remove Background - Method 1 (Buffer)
    console.log('🎨 Testing removeBackground with buffer...');
    try {
        const result = await tools.removeBackground(testImageBuffer);
        console.log('✅ RemoveBG buffer result:', JSON.stringify(result, null, 2));
    } catch (e) {
        console.log('❌ RemoveBG buffer failed:', e.message);
    }
    
    // Test 2b: Remove Background - Method 2 (Base64)
    console.log('🎨 Testing removeBackground with base64...');
    try {
        const result = await tools.removeBackground(base64Image);
        console.log('✅ RemoveBG base64 result:', JSON.stringify(result, null, 2));
    } catch (e) {
        console.log('❌ RemoveBG base64 failed:', e.message);
    }
    
    console.log('\n' + '─'.repeat(50) + '\n');
    
    // Test 3: Remini V1 - Method 1 (Buffer)
    console.log('✨ Testing reminiV1 with buffer...');
    try {
        const result = await tools.reminiV1(testImageBuffer);
        console.log('✅ ReminiV1 buffer result:', JSON.stringify(result, null, 2));
    } catch (e) {
        console.log('❌ ReminiV1 buffer failed:', e.message);
    }
    
    // Test 3b: Remini V1 - Method 2 (Base64)
    console.log('✨ Testing reminiV1 with base64...');
    try {
        const result = await tools.reminiV1(base64Image);
        console.log('✅ ReminiV1 base64 result:', JSON.stringify(result, null, 2));
    } catch (e) {
        console.log('❌ ReminiV1 base64 failed:', e.message);
    }
    
    console.log('\n' + '─'.repeat(50) + '\n');
    
    // Test 4: Remini V2 - Method 1 (Buffer)
    console.log('⚡ Testing reminiV2 with buffer...');
    try {
        const result = await tools.reminiV2(testImageBuffer);
        console.log('✅ ReminiV2 buffer result:', JSON.stringify(result, null, 2));
    } catch (e) {
        console.log('❌ ReminiV2 buffer failed:', e.message);
    }
    
    // Test 4b: Remini V2 - Method 2 (Base64)
    console.log('⚡ Testing reminiV2 with base64...');
    try {
        const result = await tools.reminiV2(base64Image);
        console.log('✅ ReminiV2 base64 result:', JSON.stringify(result, null, 2));
    } catch (e) {
        console.log('❌ ReminiV2 base64 failed:', e.message);
    }
    
    console.log('\n' + '═'.repeat(50));
    console.log('🏁 Test completed!');
}

// Test dengan real image jika ada
async function testWithRealImage() {
    const imagePath = path.join(__dirname, 'test.jpg');
    
    if (fs.existsSync(imagePath)) {
        console.log('\n🖼️ Testing with real image...');
        const imageBuffer = fs.readFileSync(imagePath);
        
        try {
            const result = await tools.reminiV1(imageBuffer);
            console.log('Real image test result:', JSON.stringify(result, null, 2));
        } catch (e) {
            console.log('Real image test failed:', e.message);
        }
    } else {
        console.log('\n💡 Tip: Letakkan file test.jpg di folder ini untuk test dengan gambar real');
    }
}

// Jalankan test
if (require.main === module) {
    testAITools()
        .then(() => testWithRealImage())
        .catch(console.error);
}

module.exports = { testAITools };
